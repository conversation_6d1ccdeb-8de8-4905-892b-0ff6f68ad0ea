# وثيقة متطلبات المنتج - لوحة التحكم الإدارية

# AntiqueArabia Admin Panel - Product Requirements Document

## نظرة عامة على المشروع

### وصف المشروع

لوحة التحكم الإدارية لمنصة AntiqueArabia هي واجهة ويب شاملة مصممة لإدارة جميع جوانب المنصة. تم تطويرها باستخدام Laravel مع Filament لتوفير تجربة إدارية حديثة وسهلة الاستخدام.

### الرؤية

إنشاء لوحة تحكم قوية ومرنة تمكن المديرين من إدارة المنصة بكفاءة عالية ومراقبة الأداء في الوقت الفعلي.

### الأهداف الرئيسية

- توفير واجهة إدارية شاملة وسهلة الاستخدام
- إدارة فعالة للمستخدمين والمنتجات والطلبات
- نظام تقارير وإحصائيات متقدم
- نظام صلاحيات مرن ومتدرج

## المتطلبات الوظيفية

### 1. لوحة المعلومات الرئيسية (Dashboard)

- إحصائيات المبيعات اليومية والشهرية والسنوية
- عدد المستخدمين الجدد والنشطين
- أحدث الطلبات والمنتجات
- مخططات بيانية تفاعلية
- تنبيهات النظام والإشعارات المهمة
- ملخص الأداء العام للمنصة

### 2. إدارة المستخدمين (User Management)

- عرض قائمة جميع المستخدمين مع الفلترة والبحث
- إضافة وتعديل وحذف المستخدمين
- إدارة حالات المستخدمين (نشط، معطل، محظور)
- عرض تفاصيل المستخدم وسجل النشاطات
- إدارة الأدوار والصلاحيات
- تصدير بيانات المستخدمين

### 3. إدارة المنتجات (Product Management)

- عرض قائمة المنتجات مع الفلترة المتقدمة
- إضافة منتجات جديدة مع معرض الصور
- تعديل تفاصيل المنتجات والأسعار
- إدارة المخزون وتتبع الكميات
- إدارة فئات وتصنيفات المنتجات
- نظام الموافقة على المنتجات للبائعين
- إدارة العروض والخصومات

### 4. إدارة الطلبات (Order Management)

- عرض جميع الطلبات مع حالاتها المختلفة
- تحديث حالة الطلبات (قيد المعالجة، مشحون، مسلم)
- إدارة المرتجعات والاستردادات
- طباعة الفواتير وأوراق الشحن
- تتبع الشحنات
- إدارة شكاوى العملاء

### 5. إدارة المدفوعات والمالية (Financial Management)

- عرض تقارير المبيعات والإيرادات
- إدارة وسائل الدفع والعمولات
- تتبع المدفوعات والاستردادات
- إدارة الفواتير والضرائب
- تقارير مالية شاملة
- إدارة حسابات البائعين والعمولات

### 6. إدارة المحتوى (Content Management)

- إدارة الصفحات الثابتة (من نحن، سياسة الخصوصية)
- إدارة البانرات والإعلانات
- إدارة الأخبار والمقالات
- إدارة الأسئلة الشائعة
- إدارة التقييمات والمراجعات
- إدارة الإشعارات للمستخدمين

### 7. التقارير والإحصائيات (Reports & Analytics)

- تقارير المبيعات التفصيلية
- إحصائيات المستخدمين والنشاطات
- تقارير المنتجات الأكثر مبيعاً
- تحليل سلوك المستخدمين
- تقارير الأداء المالي
- تصدير التقارير بصيغ مختلفة (PDF, Excel)

### 8. إعدادات النظام (System Settings)

- إعدادات عامة للمنصة
- إدارة العملات وأسعار الصرف
- إعدادات الشحن والضرائب
- إعدادات البريد الإلكتروني
- إعدادات الأمان والحماية
- إدارة النسخ الاحتياطية

## المتطلبات التقنية

### التقنيات المستخدمة

- **Framework**: Laravel 10.x
- **Admin Panel**: Filament 3.x
- **Database**: MySQL 8.0+ (مشتركة مع API)
- **PHP Version**: PHP 8.1+
- **Frontend**: Livewire, Alpine.js, Tailwind CSS
- **Charts**: Chart.js / ApexCharts
- **File Management**: Spatie Media Library

### متطلبات الخادم

- **RAM**: 4GB minimum, 8GB recommended
- **Storage**: 50GB SSD minimum
- **CPU**: 2 cores minimum, 4 cores recommended
- **Network**: 100Mbps minimum

### البيئة التطويرية

- **Development**: Laravel Sail / Docker
- **Testing**: PHPUnit, Laravel Dusk
- **Code Quality**: PHP CS Fixer, PHPStan
- **Version Control**: Git

## هيكل قاعدة البيانات (إضافي للـ API)

### جداول إضافية للوحة الإدارة

#### Admin_Users Table

```sql
- id (Primary Key)
- name (VARCHAR 255)
- email (VARCHAR 255, Unique)
- password (VARCHAR 255)
- role_id (Foreign Key)
- last_login_at (TIMESTAMP)
- is_active (BOOLEAN)
- created_at, updated_at (TIMESTAMPS)
```

#### Roles Table

```sql
- id (Primary Key)
- name (VARCHAR 255)
- slug (VARCHAR 255, Unique)
- description (TEXT)
- permissions (JSON)
- created_at, updated_at (TIMESTAMPS)
```

#### Activity_Logs Table

```sql
- id (Primary Key)
- user_id (Foreign Key)
- action (VARCHAR 255)
- model_type (VARCHAR 255)
- model_id (BIGINT)
- old_values (JSON)
- new_values (JSON)
- ip_address (VARCHAR 45)
- user_agent (TEXT)
- created_at (TIMESTAMP)
```

#### Settings Table

```sql
- id (Primary Key)
- key (VARCHAR 255, Unique)
- value (TEXT)
- type (ENUM: string, integer, boolean, json)
- group (VARCHAR 100)
- description (TEXT)
- created_at, updated_at (TIMESTAMPS)
```

## واجهات لوحة التحكم

### 1. لوحة المعلومات الرئيسية

**المكونات**:

- بطاقات الإحصائيات السريعة (المبيعات، المستخدمين، الطلبات)
- مخطط المبيعات الشهرية
- قائمة أحدث الطلبات
- قائمة المنتجات منخفضة المخزون
- تنبيهات النظام

**الوظائف**:

- تحديث البيانات في الوقت الفعلي
- فلترة البيانات حسب الفترة الزمنية
- روابط سريعة للصفحات المهمة

### 2. صفحة إدارة المستخدمين

**العرض**:

- جدول المستخدمين مع البحث والفلترة
- أعمدة: الاسم، البريد الإلكتروني، تاريخ التسجيل، الحالة
- أزرار الإجراءات: عرض، تعديل، حذف، تعطيل

**النماذج**:

- نموذج إضافة مستخدم جديد
- نموذج تعديل بيانات المستخدم
- نموذج تغيير كلمة المرور

### 3. صفحة إدارة المنتجات

**العرض**:

- جدول المنتجات مع الصور المصغرة
- فلترة حسب الفئة، الحالة، السعر
- إحصائيات سريعة للمنتجات

**النماذج**:

- نموذج إضافة منتج مع رفع الصور
- محرر نصوص غني لوصف المنتج
- إدارة المتغيرات والخصائص

### 4. صفحة إدارة الطلبات

**العرض**:

- جدول الطلبات مع الحالات الملونة
- فلترة حسب الحالة، التاريخ، المبلغ
- روابط سريعة لتفاصيل الطلب

**الوظائف**:

- تحديث حالة الطلب
- طباعة الفاتورة
- إرسال إشعارات للعملاء

## نظام الصلاحيات والأدوار

### الأدوار الأساسية

#### Super Admin

- صلاحية كاملة على جميع أجزاء النظام
- إدارة المديرين والأدوار
- الوصول لجميع التقارير والإعدادات

#### Admin

- إدارة المستخدمين والمنتجات
- إدارة الطلبات والمدفوعات
- عرض التقارير الأساسية

#### Content Manager

- إدارة المحتوى والصفحات
- إدارة المنتجات (بدون حذف)
- إدارة التقييمات والمراجعات

#### Customer Support

- عرض بيانات المستخدمين (بدون تعديل)
- إدارة الطلبات والشكاوى
- الرد على الاستفسارات

### نظام الصلاحيات

```php
// مثال على الصلاحيات
'permissions' => [
    'users' => ['view', 'create', 'edit', 'delete'],
    'products' => ['view', 'create', 'edit', 'delete'],
    'orders' => ['view', 'create', 'edit', 'delete'],
    'reports' => ['view', 'export'],
    'settings' => ['view', 'edit']
]
```

## لوحات المعلومات والإحصائيات

### المؤشرات الرئيسية (KPIs)

- إجمالي المبيعات (يومي، شهري، سنوي)
- عدد الطلبات الجديدة
- معدل التحويل
- متوسط قيمة الطلب
- عدد المستخدمين النشطين
- معدل نمو المبيعات

### المخططات البيانية

- مخطط المبيعات الزمني
- توزيع المبيعات حسب الفئات
- إحصائيات المستخدمين الجغرافية
- أداء المنتجات الأكثر مبيعاً

### التقارير المتقدمة

- تقرير الأداء المالي الشامل
- تحليل سلوك العملاء
- تقرير المخزون والمنتجات
- تقرير العمولات والمدفوعات

## متطلبات الأمان

### 1. المصادقة والتوكينات

- نظام تسجيل دخول آمن للمديرين
- جلسات محدودة الوقت
- تسجيل خروج تلقائي عند عدم النشاط
- تشفير كلمات المرور

### 2. تسجيل النشاطات

- تسجيل جميع العمليات الإدارية
- تتبع التغييرات في البيانات الحساسة
- سجل تسجيل الدخول والخروج
- تنبيهات الأنشطة المشبوهة

### 3. حماية البيانات

- تشفير البيانات الحساسة
- نسخ احتياطية منتظمة
- حماية من هجمات CSRF و XSS
- تطبيق مبدأ الصلاحيات الأدنى

## متطلبات الأداء

### أهداف الأداء

- زمن تحميل الصفحات: أقل من 2 ثانية
- زمن تحميل التقارير: أقل من 5 ثواني
- دعم 50 مدير متزامن
- معدل التوفر: 99.9%

### استراتيجيات التحسين

- تخزين مؤقت للبيانات الثابتة
- تحسين استعلامات قاعدة البيانات
- ضغط الأصول والصور
- تحميل البيانات بشكل تدريجي

## الجدول الزمني للتطوير

### المرحلة الأولى (3 أسابيع)

- إعداد Filament وتخصيص الواجهة
- تطوير لوحة المعلومات الرئيسية
- إدارة المستخدمين والأدوار

### المرحلة الثانية (3 أسابيع)

- إدارة المنتجات والفئات
- إدارة الطلبات والمدفوعات
- نظام رفع وإدارة الملفات

### المرحلة الثالثة (2 أسابيع)

- التقارير والإحصائيات
- إدارة المحتوى والإعدادات
- نظام الإشعارات

### المرحلة الرابعة (2 أسابيع)

- الاختبارات الشاملة
- تحسين الأداء والأمان
- التوثيق والتدريب

## معايير القبول

### الوظائف الأساسية

- ✅ جميع صفحات الإدارة تعمل بشكل صحيح
- ✅ نظام الصلاحيات يعمل كما هو مطلوب
- ✅ التقارير دقيقة ومحدثة
- ✅ واجهة المستخدم سهلة ومتجاوبة

### الأداء والأمان

- ✅ تحقيق أهداف الأداء المحددة
- ✅ تطبيق جميع معايير الأمان
- ✅ تسجيل شامل للنشاطات
- ✅ نسخ احتياطية تلقائية

### الجودة والاستخدام

- ✅ واجهة سهلة الاستخدام
- ✅ تصميم متجاوب لجميع الأجهزة
- ✅ توثيق شامل للمستخدمين
- ✅ تدريب الفريق الإداري

## مكونات Filament المخصصة

### Widgets مخصصة

```php
// Sales Overview Widget
class SalesOverviewWidget extends BaseWidget
{
    protected static string $view = 'filament.widgets.sales-overview';

    public function getViewData(): array
    {
        return [
            'totalSales' => Order::sum('total_amount'),
            'todaySales' => Order::whereDate('created_at', today())->sum('total_amount'),
            'ordersCount' => Order::count(),
            'usersCount' => User::count(),
        ];
    }
}
```

### Custom Pages

```php
// Analytics Page
class AnalyticsPage extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-chart-bar';
    protected static string $view = 'filament.pages.analytics';

    public function getWidgets(): array
    {
        return [
            SalesChartWidget::class,
            TopProductsWidget::class,
            UserGrowthWidget::class,
        ];
    }
}
```

### Resource Customizations

```php
// Product Resource with custom actions
class ProductResource extends Resource
{
    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                ImageColumn::make('image'),
                TextColumn::make('name')->searchable(),
                TextColumn::make('price')->money('SAR'),
                TextColumn::make('stock_quantity'),
                BadgeColumn::make('status'),
            ])
            ->actions([
                EditAction::make(),
                Action::make('duplicate')
                    ->icon('heroicon-o-document-duplicate')
                    ->action(fn (Product $record) => $this->duplicateProduct($record)),
            ]);
    }
}
```

## تخصيص الواجهة والتصميم

### Theme Customization

```css
/* Custom CSS for AntiqueArabia branding */
:root {
  --primary-50: #fdf4e7;
  --primary-500: #d4a574;
  --primary-600: #b8935f;
  --primary-700: #9c7a4a;
}

.fi-sidebar-nav {
  background: linear-gradient(135deg, #d4a574 0%, #b8935f 100%);
}

.fi-logo {
  filter: brightness(0) invert(1);
}
```

### Arabic RTL Support

```php
// في AppServiceProvider
public function boot()
{
    Filament::serving(function () {
        Filament::registerRenderHook(
            'head.end',
            fn () => view('filament.rtl-styles')
        );
    });
}
```

## إدارة الملفات والوسائط

### Media Library Integration

```php
// Product Model with Media
class Product extends Model implements HasMedia
{
    use InteractsWithMedia;

    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('images')
            ->acceptsMimeTypes(['image/jpeg', 'image/png'])
            ->singleFile();

        $this->addMediaCollection('gallery')
            ->acceptsMimeTypes(['image/jpeg', 'image/png']);
    }
}
```

### File Upload Configuration

```php
// في config/filament.php
'default_filesystem_disk' => env('FILAMENT_FILESYSTEM_DRIVER', 's3'),

'assets' => [
    'favicon' => asset('images/favicon.png'),
    'logo' => asset('images/logo.svg'),
],
```

## نظام التنبيهات والإشعارات

### Real-time Notifications

```php
// Order Status Notification
class OrderStatusChanged extends Notification
{
    public function via($notifiable)
    {
        return ['database', 'broadcast'];
    }

    public function toArray($notifiable)
    {
        return [
            'title' => 'Order Status Updated',
            'message' => "Order #{$this->order->order_number} status changed to {$this->order->status}",
            'action_url' => route('filament.resources.orders.view', $this->order),
        ];
    }
}
```

### System Alerts Widget

```php
class SystemAlertsWidget extends Widget
{
    protected static string $view = 'filament.widgets.system-alerts';

    public function getAlerts(): Collection
    {
        return collect([
            $this->checkLowStock(),
            $this->checkPendingOrders(),
            $this->checkSystemHealth(),
        ])->filter();
    }
}
```

## تقارير متقدمة ومخططات بيانية

### Sales Report Generator

```php
class SalesReportGenerator
{
    public function generateMonthlyReport($month, $year)
    {
        $orders = Order::whereMonth('created_at', $month)
                      ->whereYear('created_at', $year)
                      ->with(['items.product', 'user'])
                      ->get();

        return [
            'total_sales' => $orders->sum('total_amount'),
            'orders_count' => $orders->count(),
            'top_products' => $this->getTopProducts($orders),
            'sales_by_day' => $this->getSalesByDay($orders),
        ];
    }
}
```

### Chart Widgets

```php
class SalesChartWidget extends ChartWidget
{
    protected static ?string $heading = 'Monthly Sales';

    protected function getData(): array
    {
        $salesData = Order::selectRaw('MONTH(created_at) as month, SUM(total_amount) as total')
                         ->whereYear('created_at', now()->year)
                         ->groupBy('month')
                         ->pluck('total', 'month')
                         ->toArray();

        return [
            'datasets' => [
                [
                    'label' => 'Sales',
                    'data' => array_values($salesData),
                    'backgroundColor' => '#d4a574',
                ],
            ],
            'labels' => ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
        ];
    }

    protected function getType(): string
    {
        return 'line';
    }
}
```

## إدارة المخزون المتقدمة

### Stock Management Features

```php
class StockManagementResource extends Resource
{
    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('product.name'),
                TextColumn::make('current_stock')
                    ->color(fn ($record) => $record->current_stock < $record->min_stock ? 'danger' : 'success'),
                TextColumn::make('min_stock'),
                TextColumn::make('last_updated'),
            ])
            ->actions([
                Action::make('adjust_stock')
                    ->form([
                        TextInput::make('quantity')
                            ->numeric()
                            ->required(),
                        Select::make('type')
                            ->options([
                                'add' => 'Add Stock',
                                'remove' => 'Remove Stock',
                                'set' => 'Set Stock',
                            ])
                            ->required(),
                        Textarea::make('reason'),
                    ])
                    ->action(function (array $data, $record) {
                        $this->adjustStock($record, $data);
                    }),
            ]);
    }
}
```

### Automated Stock Alerts

```php
class LowStockAlert extends Command
{
    protected $signature = 'stock:check-low';

    public function handle()
    {
        $lowStockProducts = Product::whereColumn('stock_quantity', '<=', 'min_stock_level')
                                  ->get();

        foreach ($lowStockProducts as $product) {
            Notification::make()
                ->title('Low Stock Alert')
                ->body("Product {$product->name} is running low on stock")
                ->danger()
                ->sendToDatabase(User::role('admin')->get());
        }
    }
}
```

## التدريب والدعم

### خطة التدريب

- جلسات تدريبية للمديرين
- دليل المستخدم المصور
- فيديوهات تعليمية
- دعم فني مستمر

### الصيانة والتطوير

- تحديثات أمنية منتظمة
- إضافة ميزات جديدة حسب الحاجة
- مراقبة الأداء المستمرة
- دعم فني على مدار الساعة

## خطة النسخ الاحتياطي والاسترداد

### استراتيجية النسخ الاحتياطي

```bash
# Daily database backup
0 2 * * * /usr/local/bin/mysqldump -u backup_user -p antiquearabia_db > /backups/daily/$(date +\%Y\%m\%d)_backup.sql

# Weekly full backup
0 3 * * 0 tar -czf /backups/weekly/$(date +\%Y\%m\%d)_full_backup.tar.gz /var/www/antiquearabia

# Monthly archive
0 4 1 * * aws s3 sync /backups/monthly/ s3://antiquearabia-backups/monthly/
```

### خطة استرداد الكوارث

1. **RTO (Recovery Time Objective)**: 4 ساعات
2. **RPO (Recovery Point Objective)**: 1 ساعة
3. **خطوات الاسترداد**:
   - استرداد قاعدة البيانات من آخر نسخة احتياطية
   - استرداد ملفات التطبيق
   - إعادة تكوين الخادم
   - اختبار الوظائف الأساسية

## مراقبة الأداء والصحة

### Health Checks

```php
// في routes/web.php
Route::get('/health', function () {
    return response()->json([
        'status' => 'healthy',
        'database' => DB::connection()->getPdo() ? 'connected' : 'disconnected',
        'cache' => Cache::store()->getStore()->connection()->ping() ? 'connected' : 'disconnected',
        'storage' => Storage::disk('public')->exists('test.txt') ? 'accessible' : 'inaccessible',
        'timestamp' => now()->toISOString(),
    ]);
});
```

### Performance Monitoring

```php
class PerformanceMonitoringWidget extends Widget
{
    protected static string $view = 'filament.widgets.performance-monitoring';

    public function getMetrics(): array
    {
        return [
            'response_time' => $this->getAverageResponseTime(),
            'memory_usage' => memory_get_usage(true),
            'active_users' => $this->getActiveUsersCount(),
            'error_rate' => $this->getErrorRate(),
        ];
    }
}
```
