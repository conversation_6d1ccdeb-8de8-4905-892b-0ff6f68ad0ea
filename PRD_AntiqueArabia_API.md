# وثيقة متطلبات المنتج - واجهة برمجة التطبيقات (API)

# AntiqueArabia API - Product Requirements Document

## نظرة عامة على المشروع

### وصف المشروع

AntiqueArabia API هي واجهة برمجة التطبيقات الخلفية لمنصة AntiqueArabia المتخصصة في بيع وعرض التحف والقطع الأثرية العربية. تهدف هذه الواجهة إلى توفير خدمات شاملة وآمنة لدعم التطبيق المحمول ولوحة التحكم الإدارية.

### الرؤية

إنشاء واجهة برمجة تطبيقات قوية وقابلة للتوسع تدعم نمو منصة AntiqueArabia وتوفر تجربة مستخدم متميزة.

### الأهداف الرئيسية

- توفير واجهة برمجة تطبيقات RESTful آمنة وموثوقة
- دعم عمليات التجارة الإلكترونية الكاملة
- ضمان الأداء العالي وقابلية التوسع
- تطبيق أعلى معايير الأمان والحماية

## المتطلبات الوظيفية

### 1. إدارة المستخدمين (User Management)

- تسجيل المستخدمين الجدد
- تسجيل الدخول والخروج
- إدارة الملفات الشخصية
- نظام استرداد كلمة المرور
- التحقق من البريد الإلكتروني
- دعم تسجيل الدخول عبر وسائل التواصل الاجتماعي

### 2. إدارة المنتجات (Product Management)

- عرض قائمة المنتجات مع الفلترة والبحث
- تفاصيل المنتج الكاملة
- إدارة فئات المنتجات
- نظام التقييمات والمراجعات
- إدارة المخزون
- رفع وإدارة صور المنتجات

### 3. إدارة الطلبات (Order Management)

- إنشاء وإدارة الطلبات
- تتبع حالة الطلبات
- إدارة سلة التسوق
- حساب الضرائب والشحن
- إدارة المرتجعات والاستردادات

### 4. نظام المدفوعات (Payment System)

- معالجة المدفوعات الآمنة
- دعم وسائل الدفع المحلية (مدى، STC Pay)
- دعم البطاقات الائتمانية
- إدارة الفواتير والإيصالات

### 5. إدارة المحتوى (Content Management)

- إدارة الصفحات الثابتة
- إدارة الإشعارات
- نظام المفضلة
- إدارة التعليقات والمراجعات

## المتطلبات التقنية

### التقنيات المستخدمة

- **Framework**: Laravel 10.x
- **Authentication**: Laravel Sanctum
- **Database**: MySQL 8.0+
- **PHP Version**: PHP 8.1+
- **Cache**: Redis
- **File Storage**: AWS S3 / Local Storage
- **Queue System**: Laravel Queue with Redis

### متطلبات الخادم

- **RAM**: 4GB minimum, 8GB recommended
- **Storage**: 50GB SSD minimum
- **CPU**: 2 cores minimum, 4 cores recommended
- **Network**: 100Mbps minimum

### البيئة التطويرية

- **Development**: Laravel Sail / Docker
- **Testing**: PHPUnit, Laravel Dusk
- **Code Quality**: PHP CS Fixer, PHPStan
- **Documentation**: Swagger/OpenAPI

## هيكل قاعدة البيانات

### الجداول الرئيسية

#### Users Table

```sql
- id (Primary Key)
- name (VARCHAR 255)
- email (VARCHAR 255, Unique)
- email_verified_at (TIMESTAMP)
- password (VARCHAR 255)
- phone (VARCHAR 20)
- avatar (VARCHAR 255)
- date_of_birth (DATE)
- gender (ENUM: male, female)
- status (ENUM: active, inactive, suspended)
- created_at, updated_at (TIMESTAMPS)
```

#### Categories Table

```sql
- id (Primary Key)
- name (VARCHAR 255)
- slug (VARCHAR 255, Unique)
- description (TEXT)
- image (VARCHAR 255)
- parent_id (Foreign Key to categories.id)
- sort_order (INTEGER)
- is_active (BOOLEAN)
- created_at, updated_at (TIMESTAMPS)
```

#### Products Table

```sql
- id (Primary Key)
- name (VARCHAR 255)
- slug (VARCHAR 255, Unique)
- description (TEXT)
- short_description (VARCHAR 500)
- price (DECIMAL 10,2)
- sale_price (DECIMAL 10,2)
- sku (VARCHAR 100, Unique)
- stock_quantity (INTEGER)
- category_id (Foreign Key)
- vendor_id (Foreign Key to users.id)
- status (ENUM: active, inactive, draft)
- featured (BOOLEAN)
- weight (DECIMAL 8,2)
- dimensions (JSON)
- created_at, updated_at (TIMESTAMPS)
```

#### Orders Table

```sql
- id (Primary Key)
- user_id (Foreign Key)
- order_number (VARCHAR 50, Unique)
- status (ENUM: pending, processing, shipped, delivered, cancelled)
- total_amount (DECIMAL 10,2)
- tax_amount (DECIMAL 10,2)
- shipping_amount (DECIMAL 10,2)
- discount_amount (DECIMAL 10,2)
- payment_status (ENUM: pending, paid, failed, refunded)
- payment_method (VARCHAR 50)
- shipping_address (JSON)
- billing_address (JSON)
- notes (TEXT)
- created_at, updated_at (TIMESTAMPS)
```

## API Endpoints

### Authentication Endpoints

#### POST /api/auth/register

**Description**: تسجيل مستخدم جديد
**Parameters**:

```json
{
  "name": "string (required)",
  "email": "string (required, email)",
  "password": "string (required, min:8)",
  "password_confirmation": "string (required)",
  "phone": "string (optional)"
}
```

**Response**:

```json
{
  "success": true,
  "data": {
    "user": {...},
    "token": "string"
  },
  "message": "User registered successfully"
}
```

#### POST /api/auth/login

**Description**: تسجيل دخول المستخدم
**Parameters**:

```json
{
  "email": "string (required)",
  "password": "string (required)"
}
```

#### POST /api/auth/logout

**Description**: تسجيل خروج المستخدم
**Headers**: Authorization: Bearer {token}

#### POST /api/auth/forgot-password

**Description**: طلب استرداد كلمة المرور

### User Management Endpoints

#### GET /api/user/profile

**Description**: الحصول على بيانات المستخدم الحالي
**Headers**: Authorization: Bearer {token}

#### PUT /api/user/profile

**Description**: تحديث بيانات المستخدم

#### POST /api/user/change-password

**Description**: تغيير كلمة المرور

### Product Endpoints

#### GET /api/products

**Description**: الحصول على قائمة المنتجات
**Query Parameters**:

- page (integer): رقم الصفحة
- per_page (integer): عدد العناصر في الصفحة
- category_id (integer): معرف الفئة
- search (string): نص البحث
- sort_by (string): ترتيب النتائج
- min_price (decimal): أقل سعر
- max_price (decimal): أعلى سعر

#### GET /api/products/{id}

**Description**: الحصول على تفاصيل منتج محدد

#### GET /api/categories

**Description**: الحصول على قائمة الفئات

### Cart & Orders Endpoints

#### GET /api/cart

**Description**: الحصول على محتويات السلة

#### POST /api/cart/add

**Description**: إضافة منتج للسلة

#### PUT /api/cart/update/{item_id}

**Description**: تحديث كمية منتج في السلة

#### DELETE /api/cart/remove/{item_id}

**Description**: حذف منتج من السلة

#### POST /api/orders

**Description**: إنشاء طلب جديد

#### GET /api/orders

**Description**: الحصول على طلبات المستخدم

#### GET /api/orders/{id}

**Description**: الحصول على تفاصيل طلب محدد

### Payment Endpoints

#### POST /api/payments/process

**Description**: معالجة الدفع

#### GET /api/payments/methods

**Description**: الحصول على وسائل الدفع المتاحة

## متطلبات الأمان والحماية

### 1. المصادقة والتوكينات

- استخدام Laravel Sanctum للمصادقة
- انتهاء صلاحية التوكينات بعد 24 ساعة
- إمكانية إلغاء التوكينات عن بُعد
- تشفير كلمات المرور باستخدام bcrypt

### 2. حماية البيانات

- تشفير البيانات الحساسة في قاعدة البيانات
- استخدام HTTPS لجميع الاتصالات
- تطبيق CORS policies
- حماية من هجمات SQL Injection و XSS

### 3. معدل الطلبات (Rate Limiting)

- 60 طلب في الدقيقة للمستخدمين المسجلين
- 30 طلب في الدقيقة للمستخدمين غير المسجلين
- حماية خاصة لـ endpoints الحساسة

### 4. التحقق من صحة البيانات

- التحقق من جميع المدخلات
- تطهير البيانات قبل المعالجة
- استخدام Laravel Form Requests

## معالجة الأخطاء

### هيكل الاستجابة للأخطاء

```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Error description",
    "details": {...}
  }
}
```

### أكواد الأخطاء الرئيسية

- 400: Bad Request
- 401: Unauthorized
- 403: Forbidden
- 404: Not Found
- 422: Validation Error
- 429: Too Many Requests
- 500: Internal Server Error

## متطلبات الأداء

### أهداف الأداء

- زمن الاستجابة: أقل من 200ms للطلبات البسيطة
- زمن الاستجابة: أقل من 500ms للطلبات المعقدة
- معدل التوفر: 99.9%
- دعم 1000 مستخدم متزامن

### استراتيجيات التحسين

- استخدام Redis للتخزين المؤقت
- فهرسة قاعدة البيانات
- ضغط الاستجابات
- تحسين الاستعلامات

## الجدول الزمني للتطوير

### المرحلة الأولى (4 أسابيع)

- إعداد البيئة التطويرية
- تصميم قاعدة البيانات
- تطوير نظام المصادقة
- APIs إدارة المستخدمين

### المرحلة الثانية (3 أسابيع)

- APIs إدارة المنتجات
- APIs إدارة الفئات
- نظام البحث والفلترة

### المرحلة الثالثة (3 أسابيع)

- APIs إدارة الطلبات
- APIs سلة التسوق
- نظام المدفوعات

### المرحلة الرابعة (2 أسابيع)

- الاختبارات الشاملة
- تحسين الأداء
- التوثيق النهائي

### Wishlist Endpoints

#### GET /api/wishlist

**Description**: الحصول على قائمة المفضلة للمستخدم
**Headers**: Authorization: Bearer {token}

#### POST /api/wishlist/add

**Description**: إضافة منتج للمفضلة
**Parameters**:

```json
{
  "product_id": "integer (required)"
}
```

#### DELETE /api/wishlist/remove/{product_id}

**Description**: حذف منتج من المفضلة

### Reviews Endpoints

#### GET /api/products/{id}/reviews

**Description**: الحصول على تقييمات منتج

#### POST /api/products/{id}/reviews

**Description**: إضافة تقييم لمنتج
**Parameters**:

```json
{
  "rating": "integer (required, 1-5)",
  "comment": "string (optional)"
}
```

### Notifications Endpoints

#### GET /api/notifications

**Description**: الحصول على إشعارات المستخدم

#### PUT /api/notifications/{id}/read

**Description**: تحديد إشعار كمقروء

#### POST /api/notifications/mark-all-read

**Description**: تحديد جميع الإشعارات كمقروءة

## مخططات التدفق (Flow Charts)

### تدفق المصادقة

```
User Registration Flow:
1. POST /api/auth/register
2. Validate input data
3. Create user record
4. Send verification email
5. Generate access token
6. Return user data + token

User Login Flow:
1. POST /api/auth/login
2. Validate credentials
3. Generate access token
4. Update last_login_at
5. Return user data + token
```

### تدفق الطلبات

```
Order Creation Flow:
1. GET /api/cart (verify cart items)
2. POST /api/orders (create order)
3. Validate inventory
4. Calculate totals
5. Process payment
6. Update inventory
7. Send confirmation email
8. Return order details
```

### تدفق المدفوعات

```
Payment Processing Flow:
1. POST /api/payments/process
2. Validate payment method
3. Call payment gateway
4. Handle response
5. Update order status
6. Send receipt
7. Return payment result
```

## اختبارات الأداء والحمولة

### سيناريوهات الاختبار

1. **اختبار الحمولة العادية**: 100 مستخدم متزامن
2. **اختبار الحمولة القصوى**: 1000 مستخدم متزامن
3. **اختبار الضغط**: زيادة تدريجية حتى نقطة الفشل
4. **اختبار التحمل**: تشغيل مستمر لمدة 24 ساعة

### أدوات الاختبار

- **Apache JMeter**: لاختبارات الحمولة
- **Artillery.io**: لاختبارات الأداء
- **Laravel Telescope**: لمراقبة الأداء
- **New Relic**: لمراقبة الإنتاج

## التوثيق والـ API Documentation

### Swagger/OpenAPI Specification

```yaml
openapi: 3.0.0
info:
  title: AntiqueArabia API
  version: 1.0.0
  description: API for AntiqueArabia platform
servers:
  - url: https://api.antiquearabia.com/api
    description: Production server
  - url: https://staging-api.antiquearabia.com/api
    description: Staging server
```

### تفاصيل التوثيق

- استخدام Laravel API Documentation Generator
- أمثلة عملية لكل endpoint
- أكواد الاستجابة المختلفة
- نماذج البيانات (Schemas)
- دليل البدء السريع

## النشر والبيئات (Deployment & Environments)

### بيئات التطوير

1. **Development**: للتطوير المحلي
2. **Staging**: للاختبار قبل النشر
3. **Production**: البيئة الحية

### استراتيجية النشر

- **CI/CD Pipeline** باستخدام GitHub Actions
- **Docker Containers** للتوزيع
- **Blue-Green Deployment** لتقليل وقت التوقف
- **Database Migrations** التلقائية

### مراقبة النظام

- **Laravel Horizon**: لمراقبة الطوابير
- **Laravel Telescope**: للتطوير والتشخيص
- **Sentry**: لتتبع الأخطاء
- **Grafana**: للمراقبة والتنبيهات

## معايير القبول

### الوظائف الأساسية

- ✅ جميع APIs تعمل وفقاً للمواصفات
- ✅ نظام المصادقة آمن وفعال
- ✅ معالجة الأخطاء شاملة
- ✅ التوثيق مكتمل ومحدث

### الأداء والأمان

- ✅ تحقيق أهداف الأداء المحددة
- ✅ اجتياز اختبارات الأمان
- ✅ تطبيق جميع معايير الحماية
- ✅ اختبارات الحمولة ناجحة

### الجودة

- ✅ تغطية الاختبارات 80%+
- ✅ كود نظيف ومنظم
- ✅ توثيق شامل
- ✅ مراجعة الكود مكتملة

### النشر والصيانة

- ✅ نشر تلقائي يعمل بسلاسة
- ✅ مراقبة النظام فعالة
- ✅ نسخ احتياطية منتظمة
- ✅ خطة استرداد الكوارث جاهزة
